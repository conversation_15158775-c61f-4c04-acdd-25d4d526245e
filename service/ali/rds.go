package ali

import (
	"crypto/tls"
	"fmt"
	"html"
	"net/smtp"
	"os"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	sutil "gitlab.dailyyoga.com.cn/rdc/serverops/service/util"
)

// RDS慢查询日志配置
// 包含慢查询收集的核心配置参数
type RDSSlowLogConfig struct {
	RegionId           string   // 阿里云区域ID，如：cn-hangzhou
	PageSize           int      // API分页大小，建议100
	IgnoreDBInstanceID []string // 需要忽略的实例ID列表
}

// RDS实例信息
// 根据阿里云RDS MySQL版API文档：https://help.aliyun.com/zh/rds/apsaradb-rds-for-mysql/api-rds-2014-08-15-describedbinstances-mysql
// DBInstance 返回参数结构
type DBInstance struct {
	BpeEnabled                   string   // 弃用参数
	BurstingEnabled              bool     // 是否已开启 IO 突发
	Category                     string   // 实例系列：Basic（基础系列）、HighAvailability（高可用系列）、Finance（三节点企业系列）
	IsAnalyticReadOnlyIns        bool     // 实例是否属于MySQL DuckDB 分析实例
	ConnectionMode               string   // 实例的访问模式：Standard（标准访问模式）、Safe（数据库代理模式）
	ConnectionString             string   // 实例的连接地址
	CreateTime                   string   // 创建时间，格式：yyyy-MM-ddTHH:mm:ssZ（UTC 时间）
	DBInstanceCPU                string   // 实例 CPU 数量
	DBInstanceClass              string   // 实例规格
	DBInstanceDescription        string   // 实例描述
	DBInstanceId                 string   // 实例 ID
	DBInstanceMemory             int      // 节点的内存大小，单位：MB
	DBInstanceNetType            string   // 实例的网络连接类型：Internet（外网连接）、Intranet（内网连接）
	DBInstanceStatus             string   // 实例状态
	DBInstanceStorageType        string   // 实例储存类型
	DBInstanceType               string   // 实例类型：Primary（主实例）、Readonly（只读实例）、Guard（灾备实例）、Temp（临时实例）
	DedicatedHostGroupId         string   // 专属集群 ID
	DedicatedHostGroupName       string   // 专属集群名称
	DedicatedHostIdForLog        string   // Log 节点所在主机的 ID
	DedicatedHostIdForMaster     string   // Master 节点所在主机的 ID
	DedicatedHostIdForSlave      string   // Slave 节点所在主机的 ID
	DedicatedHostNameForLog      string   // Log 节点所在主机的名称
	DedicatedHostNameForMaster   string   // Master 节点所在主机的名称
	DedicatedHostNameForSlave    string   // Slave 节点所在主机的名称
	DedicatedHostZoneIdForLog    string   // Log 节点所在主机的可用区 ID
	DedicatedHostZoneIdForMaster string   // Master 节点所在主机的可用区 ID
	DedicatedHostZoneIdForSlave  string   // Slave 节点所在主机的可用区 ID
	DeletionProtection           bool     // 是否已开启释放保护功能
	DestroyTime                  string   // 销毁时间，格式：yyyy-MM-ddTHH:mm:ssZ（UTC 时间）
	Engine                       string   // 数据库类型
	EngineVersion                string   // 数据库版本
	ExpireTime                   string   // 到期时间，格式：yyyy-MM-ddTHH:mm:ssZ（UTC 时间）
	GeneralGroupName             string   // 专属集群 MySQL 通用版实例所属的组名
	GuardDBInstanceId            string   // 主实例如果有灾备实例，该参数即为灾备实例的 ID
	InstanceNetworkType          string   // 实例的网络类型：Classic（经典网络）、VPC（VPC 网络）
	IoAccelerationEnabled        string   // 是否开启 IO 加速：1（已开启）、0（未开启）
	LockMode                     string   // 实例的锁定状态
	LockReason                   string   // 实例被锁定的原因
	MasterInstanceId             string   // 主实例的 ID，如果没有返回此参数则表示该实例是主实例
	MutriORsignle                bool     // 是否是组合可用区
	PayType                      string   // 实例的付费类型：Postpaid（按量付费）、Prepaid（包年包月）
	ReadOnlyDBInstanceIds        []string // 主实例下如果有只读实例，该参数为只读实例的 ID 列表
	RegionId                     string   // 地域 ID
	ResourceGroupId              string   // 资源组 ID
	SwitchWeight                 int      // 当前专属集群 MySQL 通用版实例是否支持高可用权重切换
	TempDBInstanceId             string   // 主实例如果有临时实例，该参数即为临时实例的 ID
	Tips                         string   // 专属集群 MySQL 通用版实例的异常提示信息
	TipsLevel                    int      // 专属集群 MySQL 通用版实例的异常提示等级
	VSwitchId                    string   // 交换机 ID
	VpcCloudInstanceId           string   // 专有网络实例 ID
	VpcId                        string   // VPC ID
	VpcName                      string   // VPC 名称
	ZoneId                       string   // 可用区 ID
	BlueGreenDeploymentName      string   // 备用参数，无需配置
	BlueInstanceName             string   // 备用参数，无需配置
	GreenInstanceName            string   // 备用参数，无需配置
	AutoRenewal                  bool     // 实例是否自动续费
	ColdDataEnabled              bool     // 预留参数
}

// 慢查询日志记录
// 根据阿里云RDS MySQL版API文档：https://help.aliyun.com/zh/rds/apsaradb-rds-for-mysql/api-rds-2014-08-15-describeslowlogrecords-mysql
// SQLSlowRecord 返回参数结构
type SlowLogRecord struct {
	ApplicationName       string // 连接的应用名称（仅 SQL Server 实例支持）
	ClientHostName        string // 客户端主机名（仅 SQL Server 实例支持）
	CpuTime               int64  // CPU 处理时长，单位：毫秒（仅 SQL Server 实例支持）
	DBName                string // 数据库名称
	ExecutionStartTime    string // 执行开始时间，格式：yyyy-MM-ddTHH:mm:ssZ（UTC 时间）
	HostAddress           string // 连接数据库的客户端名称及地址
	LastRowsAffectedCount int64  // 最后一条语句的影响行数（仅 SQL Server 实例支持）
	LockTimes             int64  // 锁定时长，单位：秒
	LogicalIORead         int64  // 逻辑读次数（仅 SQL Server 实例支持）
	ParseRowCounts        int64  // 解析行数
	PhysicalIORead        int64  // 物理读次数（仅 SQL Server 实例支持）
	QueryTimeMS           int64  // 执行时长，单位：毫秒（对于 SQL Server，该参数的单位为微秒）
	QueryTimes            int64  // 执行时长，单位：秒（对于 SQL Server，该参数的单位为毫秒）
	ReturnRowCounts       int64  // 返回行数
	RowsAffectedCount     int64  // 影响行数（仅 SQL Server 实例支持）
	SQLHash               string // 慢日志明细里的 SQL 语句唯一标识符
	SQLText               string // SQL 命令详情
	UserName              string // 用户名（仅 SQL Server 实例支持）
	WriteIOCount          int64  // I/O 写入次数（仅 SQL Server 实例支持）
}

// @return OpenApi.Params
func CreateRdsApiInfo(action string) (_result *openapi.Params) {
	params := &openapi.Params{
		// 接口名称
		Action: tea.String(action),
		// 接口版本
		Version: tea.String("2014-08-15"),
		// 接口协议
		Protocol: tea.String("HTTPS"),
		// 接口 HTTP 方法
		Method:   tea.String("POST"),
		AuthType: tea.String("AK"),
		Style:    tea.String("RPC"),
		// 接口 PATH
		Pathname: tea.String("/"),
		// 接口请求体内容格式
		ReqBodyType: tea.String("json"),
		// 接口响应体内容格式
		BodyType: tea.String("json"),
	}
	_result = params
	return _result
}

// 获取单个实例的慢查询日志记录
// 调用阿里云RDS API获取指定实例在指定时间范围内的慢查询日志
// 参数:
//   - instanceId: RDS实例ID
//   - startTime: 查询开始时间，格式：2006-01-02T15:04Z
//   - endTime: 查询结束时间，格式：2006-01-02T15:04Z
//   - pageNumber: 页码，从1开始
//   - pageSize: 每页记录数，最大100
// 返回:
//   - []SlowLogRecord: 慢查询记录列表
//   - int: 总记录数
//   - error: 错误信息
func describeSlowLogRecords(instanceId, startTime, endTime string, pageNumber, pageSize int) ([]SlowLogRecord, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeSlowLogRecords")
	// query params
	queries := map[string]interface{}{}
	queries["DBInstanceId"] = tea.String(instanceId)
	queries["StartTime"] = tea.String(startTime)
	queries["EndTime"] = tea.String(endTime)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))
	// 可选参数
	// queries["SQLId"] = tea.Int64(0) // 可选，SQL ID
	// queries["DBName"] = tea.String("") // 可选，数据库名称

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "IncorrectDBInstanceState") {
			return nil, 0, fmt.Errorf("实例状态不正确，无法执行操作: %v", err)
		} else if strings.Contains(err.Error(), "InvalidDBInstanceId.NotFound") {
			return nil, 0, fmt.Errorf("指定的实例不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Format") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Format") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Malformed") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Malformed") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeSlowLogRecords API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := sutil.SafeToString(body["RequestId"])
	logger.Debugf("DescribeSlowLogRecords API请求ID: %s", requestId)

	// 检查引擎
	engine, _ := sutil.SafeToString(body["Engine"])
	if engine != "MySQL" {
		logger.Warnf("非MySQL引擎: %s", engine)
	}

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	sqlSlowRecords, ok := items["SQLSlowRecord"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("实例 %s 没有慢查询记录", instanceId)
		return []SlowLogRecord{}, 0, nil
	}

	// 获取总记录数
	totalRecordCount := 0
	if count, ok := sutil.SafeToInt(body["TotalRecordCount"]); ok {
		totalRecordCount = count
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := sutil.SafeToInt(body["PageNumber"]); ok {
		pageNum = pn
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := sutil.SafeToInt(body["PageSize"]); ok {
		pageSizeReturned = ps
	}

	logger.Debugf("慢查询日志分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalRecordCount)

	// 解析慢查询记录
	records := make([]SlowLogRecord, 0, len(sqlSlowRecords))
	for _, record := range sqlSlowRecords {
		r, ok := record.(map[string]interface{})
		if !ok {
			continue
		}

		slowLog := SlowLogRecord{}
		// 根据阿里云文档的 SQLSlowRecord 结构解析字段
		if v, ok := sutil.SafeToString(r["ApplicationName"]); ok {
			slowLog.ApplicationName = v
		}
		if v, ok := sutil.SafeToString(r["ClientHostName"]); ok {
			slowLog.ClientHostName = v
		}
		if v, ok := sutil.SafeToInt64(r["CpuTime"]); ok {
			slowLog.CpuTime = v
		}
		if v, ok := sutil.SafeToString(r["DBName"]); ok {
			slowLog.DBName = v
		}
		if v, ok := sutil.SafeToString(r["ExecutionStartTime"]); ok {
			slowLog.ExecutionStartTime = v
		}
		if v, ok := sutil.SafeToString(r["HostAddress"]); ok {
			slowLog.HostAddress = v
		}
		if v, ok := sutil.SafeToInt64(r["LastRowsAffectedCount"]); ok {
			slowLog.LastRowsAffectedCount = v
		}
		if v, ok := sutil.SafeToInt64(r["LockTimes"]); ok {
			slowLog.LockTimes = v
		}
		if v, ok := sutil.SafeToInt64(r["LogicalIORead"]); ok {
			slowLog.LogicalIORead = v
		}
		if v, ok := sutil.SafeToInt64(r["ParseRowCounts"]); ok {
			slowLog.ParseRowCounts = v
		}
		if v, ok := sutil.SafeToInt64(r["PhysicalIORead"]); ok {
			slowLog.PhysicalIORead = v
		}
		if v, ok := sutil.SafeToInt64(r["QueryTimeMS"]); ok {
			slowLog.QueryTimeMS = v
		}
		if v, ok := sutil.SafeToInt64(r["QueryTimes"]); ok {
			slowLog.QueryTimes = v
		}
		if v, ok := sutil.SafeToInt64(r["ReturnRowCounts"]); ok {
			slowLog.ReturnRowCounts = v
		}
		if v, ok := sutil.SafeToInt64(r["RowsAffectedCount"]); ok {
			slowLog.RowsAffectedCount = v
		}
		if v, ok := sutil.SafeToString(r["SQLHash"]); ok {
			slowLog.SQLHash = v
		}
		if v, ok := sutil.SafeToString(r["SQLText"]); ok {
			slowLog.SQLText = v
		}
		if v, ok := sutil.SafeToString(r["UserName"]); ok {
			slowLog.UserName = v
		}
		if v, ok := sutil.SafeToInt64(r["WriteIOCount"]); ok {
			slowLog.WriteIOCount = v
		}
		records = append(records, slowLog)
	}

	return records, totalRecordCount, nil
}

// 获取所有慢查询日志记录（处理分页）
// 自动处理分页，获取指定实例的所有慢查询日志记录
// 参数:
//   - instanceId: RDS实例ID
//   - startTime: 查询开始时间
//   - endTime: 查询结束时间
//   - pageSize: 每页记录数
// 返回:
//   - []SlowLogRecord: 所有慢查询记录
//   - error: 错误信息
func GetAllSlowLogRecords(instanceId, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	var allRecords []SlowLogRecord
	pageNumber := 1

	for {
		records, totalCount, err := describeSlowLogRecords(instanceId, startTime, endTime, pageNumber, pageSize)
		if err != nil {
			return nil, err
		}

		allRecords = append(allRecords, records...)

		// 判断是否已获取所有记录
		if len(allRecords) >= totalCount || len(records) == 0 {
			break
		}

		pageNumber++
	}

	return allRecords, nil
}

// 获取RDS实例列表（支持分页）
func DescribeDBInstances(regionId string, instanceType string, pageNumber, pageSize int) ([]DBInstance, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeDBInstances")
	// query params
	queries := map[string]interface{}{}
	queries["Engine"] = tea.String("MySQL")
	queries["DBInstanceStatus"] = tea.String("Running")
	if instanceType != "" {
		queries["DBInstanceType"] = tea.String(instanceType)
	}
	queries["RegionId"] = tea.String(regionId)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))

	// 可选参数
	// queries["ResourceGroupId"] = tea.String("") // 可选，资源组ID
	// queries["InstanceNetworkType"] = tea.String("") // 可选，实例网络类型
	// queries["ConnectionMode"] = tea.String("") // 可选，实例连接模式
	// queries["Tags"] = tea.String("") // 可选，标签
	// queries["VSwitchId"] = tea.String("") // 可选，交换机ID
	// queries["VpcId"] = tea.String("") // 可选，VPC ID
	// queries["ZoneId"] = tea.String("") // 可选，可用区ID
	// queries["PayType"] = tea.String("") // 可选，付费类型

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "InvalidRegionId.NotFound") {
			return nil, 0, fmt.Errorf("指定的地域不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidParameter") {
			return nil, 0, fmt.Errorf("参数错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeDBInstances API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := sutil.SafeToString(body["RequestId"])
	logger.Debugf("DescribeDBInstances API请求ID: %s", requestId)

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	dbInstances, ok := items["DBInstance"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("区域 %s 没有符合条件的RDS实例", regionId)
		return []DBInstance{}, 0, nil
	}

	// 获取总记录数
	totalCount := 0
	if count, ok := sutil.SafeToInt(body["TotalRecordCount"]); ok {
		totalCount = count
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := sutil.SafeToInt(body["PageNumber"]); ok {
		pageNum = pn
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := sutil.SafeToInt(body["PageSize"]); ok {
		pageSizeReturned = ps
	}

	logger.Debugf("RDS实例列表分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalCount)

	// 解析实例信息
	instances := make([]DBInstance, 0)
	for _, instance := range dbInstances {
		i, ok := instance.(map[string]interface{})
		if !ok {
			continue
		}

		dbInstance := DBInstance{}

		// 根据阿里云文档的 DBInstance 结构解析字段
		if v, ok := sutil.SafeToString(i["BpeEnabled"]); ok {
			dbInstance.BpeEnabled = v
		}
		if v, ok := i["BurstingEnabled"].(bool); ok {
			dbInstance.BurstingEnabled = v
		}
		if v, ok := sutil.SafeToString(i["Category"]); ok {
			dbInstance.Category = v
		}
		if v, ok := i["IsAnalyticReadOnlyIns"].(bool); ok {
			dbInstance.IsAnalyticReadOnlyIns = v
		}
		if v, ok := sutil.SafeToString(i["ConnectionMode"]); ok {
			dbInstance.ConnectionMode = v
		}
		if v, ok := sutil.SafeToString(i["ConnectionString"]); ok {
			dbInstance.ConnectionString = v
		}
		if v, ok := sutil.SafeToString(i["CreateTime"]); ok {
			dbInstance.CreateTime = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceCPU"]); ok {
			dbInstance.DBInstanceCPU = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceClass"]); ok {
			dbInstance.DBInstanceClass = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceDescription"]); ok {
			dbInstance.DBInstanceDescription = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceId"]); ok {
			dbInstance.DBInstanceId = v
		}
		if v, ok := sutil.SafeToInt64(i["DBInstanceMemory"]); ok {
			dbInstance.DBInstanceMemory = int(v)
		}
		if v, ok := sutil.SafeToString(i["DBInstanceNetType"]); ok {
			dbInstance.DBInstanceNetType = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceStatus"]); ok {
			dbInstance.DBInstanceStatus = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceStorageType"]); ok {
			dbInstance.DBInstanceStorageType = v
		}
		if v, ok := sutil.SafeToString(i["DBInstanceType"]); ok {
			dbInstance.DBInstanceType = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostGroupId"]); ok {
			dbInstance.DedicatedHostGroupId = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostGroupName"]); ok {
			dbInstance.DedicatedHostGroupName = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostIdForLog"]); ok {
			dbInstance.DedicatedHostIdForLog = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostIdForMaster"]); ok {
			dbInstance.DedicatedHostIdForMaster = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostIdForSlave"]); ok {
			dbInstance.DedicatedHostIdForSlave = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostNameForLog"]); ok {
			dbInstance.DedicatedHostNameForLog = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostNameForMaster"]); ok {
			dbInstance.DedicatedHostNameForMaster = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostNameForSlave"]); ok {
			dbInstance.DedicatedHostNameForSlave = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostZoneIdForLog"]); ok {
			dbInstance.DedicatedHostZoneIdForLog = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostZoneIdForMaster"]); ok {
			dbInstance.DedicatedHostZoneIdForMaster = v
		}
		if v, ok := sutil.SafeToString(i["DedicatedHostZoneIdForSlave"]); ok {
			dbInstance.DedicatedHostZoneIdForSlave = v
		}
		if v, ok := i["DeletionProtection"].(bool); ok {
			dbInstance.DeletionProtection = v
		}
		if v, ok := sutil.SafeToString(i["DestroyTime"]); ok {
			dbInstance.DestroyTime = v
		}
		if v, ok := sutil.SafeToString(i["Engine"]); ok {
			dbInstance.Engine = v
		}
		if v, ok := sutil.SafeToString(i["EngineVersion"]); ok {
			dbInstance.EngineVersion = v
		}
		if v, ok := sutil.SafeToString(i["ExpireTime"]); ok {
			dbInstance.ExpireTime = v
		}
		if v, ok := sutil.SafeToString(i["GeneralGroupName"]); ok {
			dbInstance.GeneralGroupName = v
		}
		if v, ok := sutil.SafeToString(i["GuardDBInstanceId"]); ok {
			dbInstance.GuardDBInstanceId = v
		}
		if v, ok := sutil.SafeToString(i["InstanceNetworkType"]); ok {
			dbInstance.InstanceNetworkType = v
		}
		if v, ok := sutil.SafeToString(i["IoAccelerationEnabled"]); ok {
			dbInstance.IoAccelerationEnabled = v
		}
		if v, ok := sutil.SafeToString(i["LockMode"]); ok {
			dbInstance.LockMode = v
		}
		if v, ok := sutil.SafeToString(i["LockReason"]); ok {
			dbInstance.LockReason = v
		}
		if v, ok := sutil.SafeToString(i["MasterInstanceId"]); ok {
			dbInstance.MasterInstanceId = v
		}
		if v, ok := i["MutriORsignle"].(bool); ok {
			dbInstance.MutriORsignle = v
		}
		if v, ok := sutil.SafeToString(i["PayType"]); ok {
			dbInstance.PayType = v
		}
		// 处理只读实例ID列表
		if readOnlyIds, ok := i["ReadOnlyDBInstanceIds"].(map[string]interface{}); ok {
			if readOnlyList, ok := readOnlyIds["ReadOnlyDBInstanceId"].([]interface{}); ok {
				for _, readOnlyItem := range readOnlyList {
					if readOnlyMap, ok := readOnlyItem.(map[string]interface{}); ok {
						if id, ok := sutil.SafeToString(readOnlyMap["DBInstanceId"]); ok {
							dbInstance.ReadOnlyDBInstanceIds = append(dbInstance.ReadOnlyDBInstanceIds, id)
						}
					}
				}
			}
		}
		if v, ok := sutil.SafeToString(i["RegionId"]); ok {
			dbInstance.RegionId = v
		}
		if v, ok := sutil.SafeToString(i["ResourceGroupId"]); ok {
			dbInstance.ResourceGroupId = v
		}
		if v, ok := sutil.SafeToInt64(i["SwitchWeight"]); ok {
			dbInstance.SwitchWeight = int(v)
		}
		if v, ok := sutil.SafeToString(i["TempDBInstanceId"]); ok {
			dbInstance.TempDBInstanceId = v
		}
		if v, ok := sutil.SafeToString(i["Tips"]); ok {
			dbInstance.Tips = v
		}
		if v, ok := sutil.SafeToInt64(i["TipsLevel"]); ok {
			dbInstance.TipsLevel = int(v)
		}
		if v, ok := sutil.SafeToString(i["VSwitchId"]); ok {
			dbInstance.VSwitchId = v
		}
		if v, ok := sutil.SafeToString(i["VpcCloudInstanceId"]); ok {
			dbInstance.VpcCloudInstanceId = v
		}
		if v, ok := sutil.SafeToString(i["VpcId"]); ok {
			dbInstance.VpcId = v
		}
		if v, ok := sutil.SafeToString(i["VpcName"]); ok {
			dbInstance.VpcName = v
		}
		if v, ok := sutil.SafeToString(i["ZoneId"]); ok {
			dbInstance.ZoneId = v
		}
		if v, ok := sutil.SafeToString(i["BlueGreenDeploymentName"]); ok {
			dbInstance.BlueGreenDeploymentName = v
		}
		if v, ok := sutil.SafeToString(i["BlueInstanceName"]); ok {
			dbInstance.BlueInstanceName = v
		}
		if v, ok := sutil.SafeToString(i["GreenInstanceName"]); ok {
			dbInstance.GreenInstanceName = v
		}
		if v, ok := i["AutoRenewal"].(bool); ok {
			dbInstance.AutoRenewal = v
		}
		if v, ok := i["ColdDataEnabled"].(bool); ok {
			dbInstance.ColdDataEnabled = v
		}

		instances = append(instances, dbInstance)
	}

	return instances, totalCount, nil
}

// 获取所有RDS实例（包括主实例和只读实例）
func GetAllDBInstances(regionId string, pageSize int, config RDSSlowLogConfig) ([]DBInstance, error) {
	var allInstances []DBInstance

	// 获取主实例
	pageNumber := 1
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Primary", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取主实例列表失败: %v", err)
		}

		allInstances = append(allInstances, instances...)

		// 判断是否已获取所有记录
		if len(allInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 获取只读实例
	pageNumber = 1
	var readonlyInstances []DBInstance
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Readonly", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取只读实例列表失败: %v", err)
		}

		readonlyInstances = append(readonlyInstances, instances...)

		// 判断是否已获取所有记录
		if len(readonlyInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 合并主实例和只读实例
	allInstances = append(allInstances, readonlyInstances...)

	return allInstances, nil
}

// 格式化时间为阿里云API要求的格式
func FormatTime(t time.Time) string {
	return t.UTC().Format("2006-01-02T15:04Z")
}

// 收集单个实例的慢查询日志
func CollectInstanceSlowLogs(instance DBInstance, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	logger.Infof("正在收集实例 %s 的慢查询日志...", instance.DBInstanceId)

	records, err := GetAllSlowLogRecords(instance.DBInstanceId, startTime, endTime, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
	}

	logger.Infof("实例 %s 共收集到 %d 条慢查询记录", instance.DBInstanceId, len(records))
	return records, nil
}

// 获取RDS慢查询日志和实例信息（优化版本，避免重复调用GetAllDBInstances）
func CollectRDSSlowLogsWithInstances(config RDSSlowLogConfig) (map[string][]SlowLogRecord, []DBInstance, error) {
	logger.Info("开始收集RDS慢查询日志...")

	// 验证参数
	if err := ValidateAPIParams(config); err != nil {
		return nil, nil, fmt.Errorf("参数验证失败: %v", err)
	}

	// 计算查询时间范围
	now := time.Now()
	// 默认获取前一天的数据
	yesterday := now.AddDate(0, 0, -1)
	startTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location()))
	endTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, yesterday.Location()))

	logger.Infof("查询时间范围: %s 至 %s", startTime, endTime)

	// 获取所有RDS实例
	instances, err := GetAllDBInstances(config.RegionId, config.PageSize, config)
	if err != nil {
		return nil, nil, fmt.Errorf("获取RDS实例列表失败: %v", err)
	}

	if len(instances) == 0 {
		logger.Info("未找到任何RDS实例")
		return make(map[string][]SlowLogRecord), instances, nil
	}

	logger.Infof("找到 %d 个RDS实例", len(instances))

	// 收集所有实例的慢查询日志
	instanceLogs := make(map[string][]SlowLogRecord)
	successCount := 0
	failCount := 0
	ignoredCount := 0

	// 创建忽略实例的映射，便于快速查找
	ignoreMap := make(map[string]bool)
	for _, id := range config.IgnoreDBInstanceID {
		ignoreMap[id] = true
	}

	for _, instance := range instances {
		// 检查是否在忽略列表中
		if ignoreMap[instance.DBInstanceId] {
			logger.Infof("实例 %s 在忽略列表中，跳过", instance.DBInstanceId)
			ignoredCount++
			continue
		}

		records, err := CollectInstanceSlowLogs(instance, startTime, endTime, config.PageSize)
		if err != nil {
			logger.Warnf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
			failCount++
			continue
		}
		instanceLogs[instance.DBInstanceId] = records
		successCount++
	}

	logger.Infof("成功收集 %d 个实例的慢查询日志，失败 %d 个，忽略 %d 个", successCount, failCount, ignoredCount)

	// 统计总的慢查询记录数
	totalSlowQueries := 0
	for _, records := range instanceLogs {
		totalSlowQueries += len(records)
	}

	logger.Infof("RDS慢查询日志收集完成，共收集到 %d 条慢查询记录", totalSlowQueries)

	return instanceLogs, instances, nil
}

// 慢查询统计维度结构
type SlowLogSummary struct {
	CollectionTime string // 慢日志采集时间
	SQLText        string // SQL语句
	ClientIP       string // 客户端IP
	DatabaseName   string // 数据库名
	ExecutionTime  int64  // 执行时长（秒）
	LockTime       int64  // 锁定时长（秒）
	ParseRows      int64  // 解析行数
	ReturnRows     int64  // 返回行数
	InstanceId     string // 实例ID
	InstanceDesc   string // 实例描述
}

// 实例慢查询报告
type InstanceSlowLogReport struct {
	InstanceId   string
	InstanceDesc string
	SlowLogs     []SlowLogSummary
	TotalCount   int
}

// 慢查询报告
type SlowLogReport struct {
	StartTime       string
	EndTime         string
	InstanceReports []InstanceSlowLogReport
	GeneratedAt     string
	TotalInstances  int
	TotalSlowLogs   int
}

// 处理慢查询数据，按实例分组并生成统计维度
// 将原始慢查询记录转换为报告格式，包含统计维度和排序
// 统计维度包括：采集时间、SQL语句、客户端IP、数据库名、执行时长、锁定时长、解析行数、返回行数
// 参数:
//   - instanceLogs: 实例ID到慢查询记录列表的映射
//   - instances: RDS实例信息列表
// 返回:
//   - SlowLogReport: 包含所有实例慢查询统计的报告
func ProcessSlowLogData(instanceLogs map[string][]SlowLogRecord, instances []DBInstance) SlowLogReport {
	// 创建实例ID到实例信息的映射
	instanceMap := make(map[string]DBInstance)
	for _, instance := range instances {
		instanceMap[instance.DBInstanceId] = instance
	}

	var instanceReports []InstanceSlowLogReport
	totalSlowLogs := 0

	for instanceId, logs := range instanceLogs {
		if len(logs) == 0 {
			continue
		}

		// 获取实例信息
		instance, ok := instanceMap[instanceId]
		instanceDesc := ""
		if ok {
			instanceDesc = instance.DBInstanceDescription
		}

		// 转换为统计维度
		var summaries []SlowLogSummary
		for _, log := range logs {
			// 提取客户端IP（从HostAddress中提取IP部分）
			clientIP := extractIPFromHostAddress(log.HostAddress)

			summary := SlowLogSummary{
				CollectionTime: log.ExecutionStartTime,
				SQLText:        log.SQLText,
				ClientIP:       clientIP,
				DatabaseName:   log.DBName,
				ExecutionTime:  log.QueryTimes,      // 执行时长（秒）
				LockTime:       log.LockTimes,       // 锁定时长（秒）
				ParseRows:      log.ParseRowCounts,  // 解析行数
				ReturnRows:     log.ReturnRowCounts, // 返回行数
				InstanceId:     instanceId,
				InstanceDesc:   instanceDesc,
			}
			summaries = append(summaries, summary)
		}

		// 按执行时间排序（降序）
		for i := 0; i < len(summaries)-1; i++ {
			for j := 0; j < len(summaries)-i-1; j++ {
				if summaries[j].ExecutionTime < summaries[j+1].ExecutionTime {
					summaries[j], summaries[j+1] = summaries[j+1], summaries[j]
				}
			}
		}

		report := InstanceSlowLogReport{
			InstanceId:   instanceId,
			InstanceDesc: instanceDesc,
			SlowLogs:     summaries,
			TotalCount:   len(summaries),
		}

		instanceReports = append(instanceReports, report)
		totalSlowLogs += len(summaries)
	}

	// 按慢查询总数排序（降序）
	for i := 0; i < len(instanceReports)-1; i++ {
		for j := 0; j < len(instanceReports)-i-1; j++ {
			if instanceReports[j].TotalCount < instanceReports[j+1].TotalCount {
				instanceReports[j], instanceReports[j+1] = instanceReports[j+1], instanceReports[j]
			}
		}
	}

	return SlowLogReport{
		StartTime:       "",
		EndTime:         "",
		InstanceReports: instanceReports,
		GeneratedAt:     time.Now().Format("2006-01-02 15:04:05"),
		TotalInstances:  len(instanceReports),
		TotalSlowLogs:   totalSlowLogs,
	}
}

// 从HostAddress中提取IP地址
func extractIPFromHostAddress(hostAddress string) string {
	if hostAddress == "" {
		return ""
	}

	// 尝试提取IP地址，格式可能是 "ip:port" 或 "hostname[ip]" 等
	parts := strings.Split(hostAddress, ":")
	if len(parts) > 0 {
		ip := strings.TrimSpace(parts[0])
		// 移除可能的方括号
		ip = strings.Trim(ip, "[]")
		return ip
	}

	return hostAddress
}

// 生成HTML格式的慢查询报告
// 将慢查询报告转换为美观的HTML格式，包含样式和交互效果
// 特性：
//   - 响应式设计，支持不同屏幕尺寸
//   - 执行时间分级显示（红色>10秒，橙色>5秒，绿色≤5秒）
//   - SQL语句悬停显示完整内容
//   - 汇总统计信息
// 参数:
//   - report: 慢查询报告数据
// 返回:
//   - string: HTML格式的报告内容
func GenerateSlowLogHTMLReport(report SlowLogReport) string {
	var sb strings.Builder

	// HTML头部
	sb.WriteString("<!DOCTYPE html>\n")
	sb.WriteString("<html>\n<head>\n")
	sb.WriteString("<meta charset=\"UTF-8\">\n")
	sb.WriteString("<title>RDS慢查询日志报告</title>\n")
	sb.WriteString("<style>\n")
	sb.WriteString("body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }\n")
	sb.WriteString(".container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }\n")
	sb.WriteString("h1 { color: #333; text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 10px; }\n")
	sb.WriteString("h2 { color: #007bff; margin-top: 30px; }\n")
	sb.WriteString(".summary { background-color: #e9f4ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n")
	sb.WriteString(".summary-item { display: inline-block; margin-right: 30px; }\n")
	sb.WriteString(".summary-label { font-weight: bold; color: #333; }\n")
	sb.WriteString(".summary-value { color: #007bff; font-size: 18px; font-weight: bold; }\n")
	sb.WriteString("table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }\n")
	sb.WriteString("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n")
	sb.WriteString("th { background-color: #007bff; color: white; font-weight: bold; }\n")
	sb.WriteString("tr:nth-child(even) { background-color: #f9f9f9; }\n")
	sb.WriteString("tr:hover { background-color: #f0f8ff; }\n")
	sb.WriteString(".instance-header { background-color: #e6f2ff; padding: 15px; margin-top: 20px; border-radius: 5px; border-left: 4px solid #007bff; }\n")
	sb.WriteString(".sql-text { max-width: 400px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\n")
	sb.WriteString(".sql-text:hover { white-space: normal; overflow: visible; background-color: #fffacd; }\n")
	sb.WriteString(".high-time { color: #dc3545; font-weight: bold; }\n")
	sb.WriteString(".medium-time { color: #fd7e14; font-weight: bold; }\n")
	sb.WriteString(".low-time { color: #28a745; }\n")
	sb.WriteString("</style>\n")
	sb.WriteString("</head>\n<body>\n")

	// 容器开始
	sb.WriteString("<div class=\"container\">\n")

	// 报告标题
	sb.WriteString("<h1>RDS慢查询日志报告</h1>\n")

	// 汇总信息
	sb.WriteString("<div class=\"summary\">\n")
	sb.WriteString("<div class=\"summary-item\">\n")
	sb.WriteString("<span class=\"summary-label\">生成时间：</span>\n")
	sb.WriteString("<span class=\"summary-value\">" + report.GeneratedAt + "</span>\n")
	sb.WriteString("</div>\n")
	sb.WriteString("<div class=\"summary-item\">\n")
	sb.WriteString("<span class=\"summary-label\">实例总数：</span>\n")
	sb.WriteString("<span class=\"summary-value\">" + fmt.Sprintf("%d", report.TotalInstances) + "</span>\n")
	sb.WriteString("</div>\n")
	sb.WriteString("<div class=\"summary-item\">\n")
	sb.WriteString("<span class=\"summary-label\">慢查询总数：</span>\n")
	sb.WriteString("<span class=\"summary-value\">" + fmt.Sprintf("%d", report.TotalSlowLogs) + "</span>\n")
	sb.WriteString("</div>\n")
	sb.WriteString("</div>\n")

	// 实例报告
	for _, instanceReport := range report.InstanceReports {
		sb.WriteString("<div class=\"instance-header\">\n")
		sb.WriteString("<h2>实例: " + instanceReport.InstanceId + "</h2>\n")
		if instanceReport.InstanceDesc != "" {
			sb.WriteString("<p><strong>实例描述:</strong> " + instanceReport.InstanceDesc + "</p>\n")
		}
		sb.WriteString("<p><strong>慢查询数量:</strong> " + fmt.Sprintf("%d", instanceReport.TotalCount) + "</p>\n")
		sb.WriteString("</div>\n")

		// 慢查询表格
		sb.WriteString("<table>\n")
		sb.WriteString("<tr>\n")
		sb.WriteString("<th>序号</th>\n")
		sb.WriteString("<th>采集时间</th>\n")
		sb.WriteString("<th>SQL语句</th>\n")
		sb.WriteString("<th>客户端IP</th>\n")
		sb.WriteString("<th>数据库名</th>\n")
		sb.WriteString("<th>执行时长(秒)</th>\n")
		sb.WriteString("<th>锁定时长(秒)</th>\n")
		sb.WriteString("<th>解析行数</th>\n")
		sb.WriteString("<th>返回行数</th>\n")
		sb.WriteString("</tr>\n")

		for i, slowLog := range instanceReport.SlowLogs {
			sb.WriteString("<tr>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", i+1) + "</td>\n")
			sb.WriteString("<td>" + slowLog.CollectionTime + "</td>\n")
			sb.WriteString("<td class=\"sql-text\">" + html.EscapeString(slowLog.SQLText) + "</td>\n")
			sb.WriteString("<td>" + slowLog.ClientIP + "</td>\n")
			sb.WriteString("<td>" + slowLog.DatabaseName + "</td>\n")

			// 根据执行时间设置不同颜色
			timeClass := "low-time"
			if slowLog.ExecutionTime > 10 {
				timeClass = "high-time"
			} else if slowLog.ExecutionTime > 5 {
				timeClass = "medium-time"
			}
			sb.WriteString("<td class=\"" + timeClass + "\">" + fmt.Sprintf("%d", slowLog.ExecutionTime) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", slowLog.LockTime) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", slowLog.ParseRows) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", slowLog.ReturnRows) + "</td>\n")
			sb.WriteString("</tr>\n")
		}
		sb.WriteString("</table>\n")
	}

	// 容器结束
	sb.WriteString("</div>\n")

	// HTML尾部
	sb.WriteString("</body>\n</html>")

	return sb.String()
}

// 邮件配置
type EmailConfig struct {
	SMTPHost   string   // SMTP服务器地址
	SMTPPort   int      // SMTP端口
	Username   string   // 发送邮箱用户名
	Password   string   // 发送邮箱密码
	From       string   // 发件人邮箱
	FromName   string   // 发件人名称
	Recipients []string // 收件人列表
	EnableTLS  bool     // 是否启用TLS
}

// 创建默认邮件配置
func CreateDefaultEmailConfig() EmailConfig {
	return EmailConfig{
		SMTPHost:   "smtp.exmail.qq.com",
		SMTPPort:   465, // 默认端口
		Username:   "服务日志告警",
		Password:   "0p9o8i7U6Y",
		From:       "<EMAIL>",
		FromName:   "RDS慢查询监控",
		Recipients: strings.Split("<EMAIL>,<EMAIL>", ","),
		EnableTLS:  false,
	}
}

// 发送HTML邮件
// 通过SMTP服务器发送HTML格式的邮件，支持TLS加密和多收件人
// 参数:
//   - config: 邮件配置，包含SMTP服务器信息和收件人列表
//   - subject: 邮件主题
//   - htmlContent: HTML格式的邮件内容
// 返回:
//   - error: 发送失败时的错误信息
func SendHTMLEmail(config EmailConfig, subject, htmlContent string) error {
	if len(config.Recipients) == 0 {
		return fmt.Errorf("收件人列表为空")
	}

	// 构建邮件内容
	message := buildEmailMessage(config, subject, htmlContent)

	// 连接SMTP服务器
	addr := fmt.Sprintf("%s:%d", config.SMTPHost, config.SMTPPort)

	var auth smtp.Auth
	if config.Username != "" && config.Password != "" {
		auth = smtp.PlainAuth("", config.Username, config.Password, config.SMTPHost)
	}

	// 发送邮件
	if config.EnableTLS {
		return sendEmailWithTLS(addr, auth, config.From, config.Recipients, message)
	} else {
		return smtp.SendMail(addr, auth, config.From, config.Recipients, []byte(message))
	}
}

// 构建邮件消息
func buildEmailMessage(config EmailConfig, subject, htmlContent string) string {
	var sb strings.Builder

	// 邮件头部
	sb.WriteString("MIME-Version: 1.0\r\n")
	sb.WriteString("Content-Type: text/html; charset=UTF-8\r\n")

	// 发件人
	if config.FromName != "" {
		sb.WriteString(fmt.Sprintf("From: %s <%s>\r\n", config.FromName, config.From))
	} else {
		sb.WriteString(fmt.Sprintf("From: %s\r\n", config.From))
	}

	// 收件人
	sb.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(config.Recipients, ", ")))

	// 主题
	sb.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))

	// 空行分隔头部和正文
	sb.WriteString("\r\n")

	// 邮件正文
	sb.WriteString(htmlContent)

	return sb.String()
}

// 使用TLS发送邮件
func sendEmailWithTLS(addr string, auth smtp.Auth, from string, to []string, message string) error {
	// 创建TLS配置
	tlsConfig := &tls.Config{
		ServerName: strings.Split(addr, ":")[0],
	}

	// 连接服务器
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("连接SMTP服务器失败: %v", err)
	}
	defer conn.Close()

	// 创建SMTP客户端
	client, err := smtp.NewClient(conn, tlsConfig.ServerName)
	if err != nil {
		return fmt.Errorf("创建SMTP客户端失败: %v", err)
	}
	defer client.Quit()

	// 认证
	if auth != nil {
		if err := client.Auth(auth); err != nil {
			return fmt.Errorf("SMTP认证失败: %v", err)
		}
	}

	// 设置发件人
	if err := client.Mail(from); err != nil {
		return fmt.Errorf("设置发件人失败: %v", err)
	}

	// 设置收件人
	for _, recipient := range to {
		recipient = strings.TrimSpace(recipient)
		if recipient != "" {
			if err := client.Rcpt(recipient); err != nil {
				return fmt.Errorf("设置收件人 %s 失败: %v", recipient, err)
			}
		}
	}

	// 发送邮件内容
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("获取邮件写入器失败: %v", err)
	}
	defer writer.Close()

	_, err = writer.Write([]byte(message))
	if err != nil {
		return fmt.Errorf("写入邮件内容失败: %v", err)
	}

	return nil
}

// 生成邮件主题（包含日期信息）
func GenerateEmailSubject(date time.Time, totalInstances, totalSlowLogs int) string {
	dateStr := date.Format("2006-01-02")
	return fmt.Sprintf("RDS慢查询日志报告 - %s (实例数: %d, 慢查询数: %d)",
		dateStr, totalInstances, totalSlowLogs)
}

// 验证API参数
// 检查RDS慢查询收集的必要参数是否有效
func ValidateAPIParams(config RDSSlowLogConfig) error {
	// 验证区域ID
	if config.RegionId == "" {
		return fmt.Errorf("区域ID不能为空")
	}

	// 验证分页大小
	if config.PageSize <= 0 || config.PageSize > 100 {
		return fmt.Errorf("分页大小必须在1-100之间")
	}

	return nil
}

// 创建默认配置
// 返回RDS慢查询收集的默认配置，可根据实际环境调整
func CreateDefaultRDSSlowLogConfig() RDSSlowLogConfig {
	return RDSSlowLogConfig{
		RegionId:           "cn-hangzhou",                                            // 默认杭州区域
		PageSize:           100,                                                      // API分页大小
		IgnoreDBInstanceID: []string{"rm-bp111s3k9m2o06u6j", "rm-bp1o78g1nf9kt6rhc"}, // 默认忽略的测试实例
	}
}

// 运行RDS慢查询日志收集器（优化版本）
// 完整的慢查询日志收集流程，包括：
//   1. 收集所有RDS实例的慢查询日志（前一天的数据）
//   2. 处理数据并生成统计报告
//   3. 生成HTML格式报告并保存到文件
//   4. 可选：发送邮件通知（需要配置环境变量）
//
// 环境变量配置（邮件功能）：
//   - SMTP_HOST: SMTP服务器地址
//   - SMTP_USERNAME: SMTP用户名
//   - SMTP_PASSWORD: SMTP密码
//   - SMTP_FROM: 发件人邮箱
//   - SMTP_RECIPIENTS: 收件人列表，逗号分隔
//
// 返回:
//   - error: 执行过程中的错误信息
func RunRDSSlowLogCollector() error {
	logger.Info("开始运行RDS慢查询日志收集器...")
	config := CreateDefaultRDSSlowLogConfig()

	// 收集慢查询日志和实例信息（一次性获取，避免重复调用）
	instanceLogs, instances, err := CollectRDSSlowLogsWithInstances(config)
	if err != nil {
		return fmt.Errorf("收集慢查询日志失败: %v", err)
	}

	// 如果没有慢查询日志，提前返回
	if len(instanceLogs) == 0 {
		logger.Info("没有慢查询日志")
		return nil
	}

	// 处理慢查询数据
	report := ProcessSlowLogData(instanceLogs, instances)

	// 设置时间范围
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	report.StartTime = yesterday.Format("2006-01-02 00:00:00")
	report.EndTime = yesterday.Format("2006-01-02 23:59:59")

	logger.Infof("RDS慢查询日志处理完成，共 %d 个实例，%d 条慢查询记录",
		report.TotalInstances, report.TotalSlowLogs)

	// 生成HTML报告
	htmlReport := GenerateSlowLogHTMLReport(report)

	// 保存报告到文件
	reportFileName := fmt.Sprintf("./log/rds_slow_log_report_%s.html",
		yesterday.Format("20060102"))
	err = os.WriteFile(reportFileName, []byte(htmlReport), 0644)
	if err != nil {
		logger.Errorf("保存报告文件失败: %v", err)
	} else {
		logger.Infof("报告已保存到文件: %s", reportFileName)
	}

	// 发送邮件
	emailConfig := CreateDefaultEmailConfig()
	if len(emailConfig.Recipients) > 0 && emailConfig.SMTPHost != "" {
		subject := GenerateEmailSubject(yesterday, report.TotalInstances, report.TotalSlowLogs)
		err = SendHTMLEmail(emailConfig, subject, htmlReport)
		if err != nil {
			logger.Errorf("发送邮件失败: %v", err)
		} else {
			logger.Infof("邮件已成功发送给: %s", strings.Join(emailConfig.Recipients, ", "))
		}
	} else {
		logger.Info("邮件配置不完整，跳过邮件发送")
	}

	return nil
}

// 收集慢查询日志并生成报告（优化版本）
func CollectAndGenerateSlowLogReport(config RDSSlowLogConfig) (*SlowLogReport, string, error) {
	logger.Info("开始收集RDS慢查询日志并生成报告...")

	// 收集慢查询日志和实例信息（一次性获取，避免重复调用）
	instanceLogs, instances, err := CollectRDSSlowLogsWithInstances(config)
	if err != nil {
		return nil, "", fmt.Errorf("收集慢查询日志失败: %v", err)
	}

	// 如果没有慢查询日志，返回空报告
	if len(instanceLogs) == 0 {
		logger.Info("没有慢查询日志")
		emptyReport := &SlowLogReport{
			StartTime:       time.Now().AddDate(0, 0, -1).Format("2006-01-02 00:00:00"),
			EndTime:         time.Now().AddDate(0, 0, -1).Format("2006-01-02 23:59:59"),
			InstanceReports: []InstanceSlowLogReport{},
			GeneratedAt:     time.Now().Format("2006-01-02 15:04:05"),
			TotalInstances:  0,
			TotalSlowLogs:   0,
		}
		return emptyReport, "", nil
	}

	// 处理慢查询数据
	report := ProcessSlowLogData(instanceLogs, instances)

	// 设置时间范围
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	report.StartTime = yesterday.Format("2006-01-02 00:00:00")
	report.EndTime = yesterday.Format("2006-01-02 23:59:59")

	// 生成HTML报告
	htmlReport := GenerateSlowLogHTMLReport(report)

	logger.Infof("RDS慢查询日志报告生成完成，共 %d 个实例，%d 条慢查询记录",
		report.TotalInstances, report.TotalSlowLogs)

	return &report, htmlReport, nil
}
